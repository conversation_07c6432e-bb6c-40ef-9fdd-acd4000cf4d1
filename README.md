# TikTok Info Telegram Bot

A Telegram bot that fetches TikTok user information using web scraping.

## Features

- Get TikTok user profile information
- Shows followers, following, likes, videos count
- Displays account creation date and bio
- Works with both commands and direct username input

## Setup

### 1. Get a Bot Token

1. Open Telegram and search for `@BotFather`
2. Start a chat with BotFather
3. Send `/newbot` command
4. Follow the instructions to create your bot
5. Copy the bot token you receive

### 2. Configure the Bot

**Option A: Environment Variable (Recommended)**
```bash
# Windows
set BOT_TOKEN=your_bot_token_here

# Linux/Mac
export BOT_TOKEN=your_bot_token_here
```

**Option B: Edit the Code**
Replace `'YOUR_BOT_TOKEN'` in `info_tiktok.py` with your actual bot token.

### 3. Install Dependencies

```bash
pip install pyTelegramBotAPI requests
```

### 4. Run the Bot

```bash
python info_tiktok.py
```

## Usage

### Commands

- `/start` - Show welcome message
- `/help` - Show help information
- `/info <username>` - Get TikTok user info

### Examples

```
/info charlidamelio
/info @khaby.lame
```

You can also send usernames directly without commands:
```
charlidamelio
@khaby.lame
```

## Features

- ✅ User profile information
- ✅ Follower/Following counts
- ✅ Total likes and videos
- ✅ Account creation date
- ✅ Bio/signature
- ✅ Error handling
- ✅ Typing indicators

## Note

This bot uses web scraping to fetch TikTok data. TikTok may change their website structure, which could affect the bot's functionality.
