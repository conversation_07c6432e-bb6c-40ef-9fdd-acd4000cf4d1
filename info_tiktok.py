import requests
import re
from datetime import datetime

def format_number(n):
    try:
        n = int(n)
        if n >= 1_000_000:
            return f"{n / 1_000_000:.1f}M"
        elif n >= 1_000:
            return f"{n / 1_000:.1f}K"
        return str(n)
    except:
        return str(n)

def extract_with_re(html, pattern, group=1):
    m = re.search(pattern, html, re.DOTALL)
    if m:
        return m.group(group).strip()
    return 'N/A'

def get_tiktok_info(username):
    url = f"https://www.tiktok.com/@{username}"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9",
        "Referer": "https://www.tiktok.com/",
    }

    r = requests.get(url, headers=headers)
    if r.status_code != 200:
    	return 'Kse'
    html = r.text

   
    nickname = extract_with_re(html, r'"nickname":"([^"]+)"')
    signature = extract_with_re(html, r'"signature":"([^"]+)"')
    user_id = extract_with_re(html, r'"id":"(\d+)"')
    region = extract_with_re(html, r'"region":"([^"]+)"')
    create_time_unix = extract_with_re(html, r'"createTime":(\d+)')
    followers = extract_with_re(html, r'"followerCount":(\d+)')
    following = extract_with_re(html, r'"followingCount":(\d+)')
    likes = extract_with_re(html, r'"heartCount":(\d+)')
    videos = extract_with_re(html, r'"videoCount":(\d+)')

    if create_time_unix != 'N/A':
        try:
            create_time = datetime.utcfromtimestamp(int(create_time_unix)).strftime('%Y-%m-%d %H:%M:%S')
        except:
            create_time = 'N/A'
    else:
        create_time = 'N/A'

    print(f"""
━━━━━━━━━━━━━━━━━━━━━
✦ Username: @{username}
✦ Name: {nickname}
✦ Bio: {signature}
✦ ID: {user_id}
✦ Region: {region}
✦ Account Created: {create_time}
✦ Followers: {format_number(followers)}
✦ Following: {format_number(following)}
✦ Likes: {format_number(likes)}
✦ Videos: {videos}
━━━━━━━━━━━━━━━━━━━━━
""")

if __name__ == "__main__":
    user = input("[+] Enter TikTok Username: ").strip().lstrip('@')
    get_tiktok_info(user)