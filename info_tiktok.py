import requests
import re
from datetime import datetime
import telebot
import os

def format_number(n):
    try:
        n = int(n)
        if n >= 1_000_000:
            return f"{n / 1_000_000:.1f}M"
        elif n >= 1_000:
            return f"{n / 1_000:.1f}K"
        return str(n)
    except:
        return str(n)

def extract_with_re(html, pattern, group=1):
    m = re.search(pattern, html, re.DOTALL)
    if m:
        return m.group(group).strip()
    return 'N/A'

def get_tiktok_info(username):
    url = f"https://www.tiktok.com/@{username}"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9",
        "Referer": "https://www.tiktok.com/",
    }

    try:
        r = requests.get(url, headers=headers, timeout=10)
        if r.status_code != 200:
            return f"[ERROR] Could not access TikTok profile for @{username}"
        html = r.text

        nickname = extract_with_re(html, r'"nickname":"([^"]+)"')
        signature = extract_with_re(html, r'"signature":"([^"]+)"')
        user_id = extract_with_re(html, r'"id":"(\d+)"')
        region = extract_with_re(html, r'"region":"([^"]+)"')
        create_time_unix = extract_with_re(html, r'"createTime":(\d+)')
        followers = extract_with_re(html, r'"followerCount":(\d+)')
        following = extract_with_re(html, r'"followingCount":(\d+)')
        likes = extract_with_re(html, r'"heartCount":(\d+)')
        videos = extract_with_re(html, r'"videoCount":(\d+)')

        if create_time_unix != 'N/A':
            try:
                create_time = datetime.fromtimestamp(int(create_time_unix)).strftime('%Y-%m-%d %H:%M:%S')
            except:
                create_time = 'N/A'
        else:
            create_time = 'N/A'

        # Return formatted information instead of printing
        return f"""
━━━━━━━━━━━━━━━━━━━━━
✦ Username: @{username}
✦ Name: {nickname}
✦ Bio: {signature}
✦ ID: {user_id}
✦ Region: {region}
✦ Account Created: {create_time}
✦ Followers: {format_number(followers)}
✦ Following: {format_number(following)}
✦ Likes: {format_number(likes)}
✦ Videos: {videos}
━━━━━━━━━━━━━━━━━━━━━
"""
    except requests.RequestException as e:
        return f"[ERROR] Network error: {str(e)}"
    except Exception as e:
        return f"[ERROR] Error occurred: {str(e)}"

# Initialize the bot
# Replace 'YOUR_BOT_TOKEN' with your actual bot token from @BotFather
BOT_TOKEN = os.getenv('BOT_TOKEN', '**********:AAFz0Q1jf9sXxq_-Rp3leeSuzaVetDo8ulE')
bot = telebot.TeleBot(BOT_TOKEN)

@bot.message_handler(commands=['start', 'help'])
def send_welcome(message):
    welcome_text = """
[BOT] **TikTok Info Bot**

Welcome! I can help you get information about TikTok users.

**Commands:**
/start - Show this welcome message
/help - Show this help message
/info <username> - Get TikTok user information

**Usage Examples:**
/info charlidamelio
/info @khaby.lame

Just send me a TikTok username and I'll fetch the profile information for you!
"""
    bot.reply_to(message, welcome_text, parse_mode='Markdown')

@bot.message_handler(commands=['info'])
def get_tiktok_user_info(message):
    try:
        # Extract username from command
        command_parts = message.text.split()
        if len(command_parts) < 2:
            bot.reply_to(message, "[ERROR] Please provide a username!\n\nExample: /info charlidamelio")
            return

        username = command_parts[1].strip().lstrip('@')

        if not username:
            bot.reply_to(message, "[ERROR] Please provide a valid username!")
            return

        # Send "typing" action
        bot.send_chat_action(message.chat.id, 'typing')

        # Get TikTok info
        info = get_tiktok_info(username)

        # Send the result
        bot.reply_to(message, f"```\n{info}\n```", parse_mode='Markdown')

    except Exception as e:
        bot.reply_to(message, f"[ERROR] An error occurred: {str(e)}")

@bot.message_handler(func=lambda message: True)
def handle_text(message):
    # Handle direct username input (without command)
    text = message.text.strip()

    # Check if it looks like a TikTok username
    if text.startswith('@') or (len(text) > 0 and not text.startswith('/')):
        username = text.lstrip('@')

        # Send "typing" action
        bot.send_chat_action(message.chat.id, 'typing')

        # Get TikTok info
        info = get_tiktok_info(username)

        # Send the result
        bot.reply_to(message, f"```\n{info}\n```", parse_mode='Markdown')
    else:
        bot.reply_to(message, "[ERROR] Please send a TikTok username or use /info <username>")

if __name__ == "__main__":
    if BOT_TOKEN == 'YOUR_BOT_TOKEN':
        print("[ERROR] Please set your bot token!")
        print("1. Get a token from @BotFather on Telegram")
        print("2. Set the BOT_TOKEN environment variable or replace 'YOUR_BOT_TOKEN' in the code")
        print("3. Run the script again")
    else:
        print("[BOT] TikTok Info Bot is starting...")
        print("Press Ctrl+C to stop the bot")
        try:
            bot.infinity_polling()
        except KeyboardInterrupt:
            print("\n[BOT] Bot stopped!")
        except Exception as e:
            print(f"[ERROR] Error: {e}")