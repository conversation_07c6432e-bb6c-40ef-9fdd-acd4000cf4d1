# Different styles for TikTok profile display

def style_1_modern_box(username, nickname, signature, user_id, region, create_time, followers, following, likes, videos):
    """Style 1: Modern box with borders"""
    return f"""
╔══════════════════════════════════════╗
║            TIKTOK PROFILE            ║
╠══════════════════════════════════════╣
║ • Username: @{username}
║ • Name: {nickname}
║ • Bio: {signature}
║ • ID: {user_id}
║ • Region: {region}
║ • Created: {create_time}
║ • Followers: {followers}
║ • Following: {following}
║ • Likes: {likes}
║ • Videos: {videos}
╚══════════════════════════════════════╝
"""

def style_2_minimalist(username, nickname, signature, user_id, region, create_time, followers, following, likes, videos):
    """Style 2: Clean minimalist"""
    return f"""
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
                TIKTOK INFO
▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬

Username    : @{username}
Name        : {nickname}
Bio         : {signature}
ID          : {user_id}
Region      : {region}
Created     : {create_time}
Followers   : {followers}
Following   : {following}
Likes       : {likes}
Videos      : {videos}

▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬
"""

def style_3_gradient(username, nickname, signature, user_id, region, create_time, followers, following, likes, videos):
    """Style 3: Gradient style"""
    return f"""
████████████████████████████████████████
██                                    ██
██           TIKTOK PROFILE           ██
██                                    ██
████████████████████████████████████████

▶ Username: @{username}
▶ Name: {nickname}
▶ Bio: {signature}
▶ ID: {user_id}
▶ Region: {region}
▶ Created: {create_time}
▶ Followers: {followers}
▶ Following: {following}
▶ Likes: {likes}
▶ Videos: {videos}

████████████████████████████████████████
"""

def style_4_simple_lines(username, nickname, signature, user_id, region, create_time, followers, following, likes, videos):
    """Style 4: Simple with lines"""
    return f"""
═══════════════════════════════════════
           📱 TIKTOK PROFILE 📱
═══════════════════════════════════════

👤 Username: @{username}
📝 Name: {nickname}
💭 Bio: {signature}
🆔 ID: {user_id}
🌍 Region: {region}
📅 Created: {create_time}
👥 Followers: {followers}
👤 Following: {following}
❤️ Likes: {likes}
🎬 Videos: {videos}

═══════════════════════════════════════
"""

def style_5_ascii_art(username, nickname, signature, user_id, region, create_time, followers, following, likes, videos):
    """Style 5: ASCII Art style"""
    return f"""
┌─────────────────────────────────────┐
│          T I K T O K   I N F O      │
├─────────────────────────────────────┤
│                                     │
│ Username : @{username}
│ Name     : {nickname}
│ Bio      : {signature}
│ ID       : {user_id}
│ Region   : {region}
│ Created  : {create_time}
│ Followers: {followers}
│ Following: {following}
│ Likes    : {likes}
│ Videos   : {videos}
│                                     │
└─────────────────────────────────────┘
"""

def style_6_modern_card(username, nickname, signature, user_id, region, create_time, followers, following, likes, videos):
    """Style 6: Modern card style"""
    return f"""
╭─────────────────────────────────────╮
│                                     │
│        🎵 TIKTOK PROFILE 🎵         │
│                                     │
├─────────────────────────────────────┤
│                                     │
│  Username  │ @{username}
│  Name      │ {nickname}
│  Bio       │ {signature}
│  ID        │ {user_id}
│  Region    │ {region}
│  Created   │ {create_time}
│  Followers │ {followers}
│  Following │ {following}
│  Likes     │ {likes}
│  Videos    │ {videos}
│                                     │
╰─────────────────────────────────────╯
"""
